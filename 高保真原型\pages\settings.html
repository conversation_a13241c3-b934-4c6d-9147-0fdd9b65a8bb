<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }
        
        .header-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px - 80px);
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .profile-section {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            color: white;
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #667eea;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .profile-info {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .settings-section {
            background: white;
            border-radius: 20px;
            padding: 0;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            padding: 20px 25px 10px;
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-item:active {
            background-color: #f8f9fa;
        }
        
        .setting-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .setting-content {
            flex: 1;
        }
        
        .setting-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }
        
        .setting-subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .setting-arrow {
            color: #ccc;
            font-size: 16px;
        }
        
        .toggle-switch {
            width: 50px;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            position: relative;
            transition: background-color 0.3s ease;
        }
        
        .toggle-switch.active {
            background: #667eea;
        }
        
        .toggle-switch::after {
            content: '';
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        .about-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .app-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
        }
        
        .app-info {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #667eea;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 页面头部 -->
    <div class="header">
        <div class="header-left">
            <div class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="header-title">设置</div>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 个人信息 -->
        <div class="profile-section">
            <div class="profile-avatar">
                <i class="fas fa-child"></i>
            </div>
            <div class="profile-name">小明</div>
            <div class="profile-info">已学习 4 天 · 掌握 24 个字词</div>
        </div>
        
        <!-- 学习设置 -->
        <div class="settings-section">
            <div class="section-title">学习设置</div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">每日学习提醒</div>
                    <div class="setting-subtitle">每天 19:00 提醒学习</div>
                </div>
                <div class="toggle-switch active"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-volume-up"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">音效设置</div>
                    <div class="setting-subtitle">开启按钮音效</div>
                </div>
                <div class="toggle-switch active"></div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">主题设置</div>
                    <div class="setting-subtitle">彩虹主题</div>
                </div>
                <div class="setting-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 数据管理 -->
        <div class="settings-section">
            <div class="section-title">数据管理</div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">导出学习记录</div>
                    <div class="setting-subtitle">备份学习数据</div>
                </div>
                <div class="setting-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-trash"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">清除数据</div>
                    <div class="setting-subtitle">删除所有学习记录</div>
                </div>
                <div class="setting-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 帮助与反馈 -->
        <div class="settings-section">
            <div class="section-title">帮助与反馈</div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">使用帮助</div>
                    <div class="setting-subtitle">查看使用说明</div>
                </div>
                <div class="setting-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-icon">
                    <i class="fas fa-comment"></i>
                </div>
                <div class="setting-content">
                    <div class="setting-title">意见反馈</div>
                    <div class="setting-subtitle">提出建议和问题</div>
                </div>
                <div class="setting-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
        
        <!-- 关于应用 -->
        <div class="about-section">
            <div class="app-logo">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="app-info">
                <strong>儿童识字 v1.0</strong><br>
                让学习变得简单有趣<br>
                专为3-8岁儿童设计
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item inactive">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
