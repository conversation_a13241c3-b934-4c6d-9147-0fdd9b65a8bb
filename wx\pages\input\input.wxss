/**
 * 字词输入页面样式
 */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

/* 头部 */
.header {
  padding: 30rpx;
  background-color: #4a89dc;
  color: white;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

/* 批量输入区域 */
.batch-input-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
  color: #333;
}

.batch-input {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 15rpx;
  font-size: 28rpx;
  margin-bottom: 15rpx;
  box-sizing: border-box;
}

.apply-btn {
  background-color: #4a89dc;
  color: white;
  font-size: 28rpx;
  margin-top: 10rpx;
}

/* 预览区域 */
.preview-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.preview-words {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.word-item {
  padding: 10rpx 20rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  position: relative;
}

/* 新字词样式 */
.new-word {
  background-color: #e3f2fd;
  border: 1rpx solid #bbdefb;
  color: #1976d2;
}

/* 已学过字词样式 */
.learned-word {
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  color: #757575;
  padding-right: 70rpx; /* 为标签留出空间 */
}

/* 已学标签 */
.word-tag {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  background-color: #9e9e9e;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 提示区域 */
.tips-section {
  background-color: #fff8e1;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
  color: #ff9800;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

/* 底部按钮 */
.footer {
  display: flex;
  padding: 20rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
}

.back-btn {
  flex: 1;
  margin-right: 10rpx;
  background-color: #f5f5f5;
  color: #333;
}

.start-btn {
  flex: 2;
  margin-left: 10rpx;
  background-color: #4caf50;
  color: white;
}

.disabled {
  opacity: 0.6;
}

.tips-section {
  padding: 40rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  margin-bottom: 40rpx;
}

.tips-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.icon-text {
  margin-right: 10rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
}

.tip-item {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
}

.action-buttons {
  padding: 40rpx;
  display: flex;
  justify-content: space-between;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.btn-primary {
  background: #f5576c;
  color: white;
  flex: 2;
  margin-left: 20rpx;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.7);
  color: #333;
  flex: 1;
}

.btn[disabled] {
  opacity: 0.5;
}

.input-field {
  flex: 1;
  border: 4rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 30rpx;
  font-size: 36rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #f093fb;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(240, 147, 251, 0.1);
}

.tips-section {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.tips-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tips-list {
  color: white;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.tip-item {
  display: block;
  margin-bottom: 10rpx;
}

.action-buttons {
  padding: 40rpx;
  display: flex;
  gap: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-secondary {
  flex: 1;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}

.btn-primary {
  flex: 2;
  background: white;
  color: #f5576c;
}

.btn-primary[disabled] {
  background: rgba(255,255,255,0.5);
  color: rgba(245, 87, 108, 0.5);
}

.icon-text {
  font-size: inherit;
}

/* 字词输入页面样式 */

/* 批量输入区域 */
.batch-input-container {
  width: 100%;
  margin-bottom: 30rpx;
}

.batch-input-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.batch-input-field {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
}

/* 预览区域 */
.preview-container {
  width: 100%;
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.preview-words {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.preview-word {
  padding: 15rpx 25rpx;
  background-color: #f0f7ff;
  border-radius: 10rpx;
  font-size: 30rpx;
  color: #333;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #d0e6ff;
}

/* 提示区域 */
.tips-section {
  width: 100%;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.tips-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.tips-list {
  display: flex;
  flex-direction: column;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 按钮样式 */
.btn-small {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  margin-left: auto;
  display: block;
}

/* 通用卡片样式 */
.card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.glass-card {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}
