<!--
  学习模式页面
  功能：字词展示和图片辅助
-->
<view class="container gradient-bg-cyan">

  
  <!-- 进度条 -->
  <view class="progress-section">
    <view class="progress-info">
      <text class="progress-text">学习进度 {{currentIndex + 1}}/{{totalWords}}</text>
      <text class="progress-word">正在学习：{{currentWord}}</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progressPercent}}%"></view>
    </view>
  </view>
  
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 字词展示区域 -->
    <view class="word-display-section">
      <view class="word-card">
        <text class="word-text">{{currentWord}}</text>
        <view class="word-pinyin" wx:if="{{currentPinyin}}">
          <text>{{currentPinyin}}</text>
        </view>
      </view>
      
      <!-- 图片展示区域已移除 -->
    </view>
    
    <!-- 学习提示 -->
    <view class="learning-tips glass-card">
      <view class="tips-title">
        <text class="icon-text">👨‍🏫</text>
        <text>学习指导</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">1. 大声朗读字词，让孩子跟读</text>
        <text class="tip-item">2. 解释字词的含义和用法</text>
        <text class="tip-item">3. 展示相关图片或实物</text>
        <text class="tip-item">4. 鼓励孩子造句或联想</text>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn btn-secondary" bindtap="previousWord" disabled="{{currentIndex === 0}}">
      <text class="icon-text">⬅️</text>
      <text>上一个</text>
    </button>
    <button class="btn btn-primary" bindtap="nextWord">
      <text class="icon-text">{{isLastWord ? '✅' : '➡️'}}</text>
      <text>{{isLastWord ? '完成学习' : '下一个'}}</text>
    </button>
  </view>
</view>
