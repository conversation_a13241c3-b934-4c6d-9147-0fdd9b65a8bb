<!--
  儿童识字小程序 - 首页
  功能：学习入口和进度展示
-->
<view class="container gradient-bg-blue">

  
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="app-icon">
        <text class="icon-text">📚</text>
      </view>
      <view class="welcome-text">
        <text class="welcome-title">儿童识字</text>
        <text class="welcome-subtitle">让学习变得简单有趣</text>
      </view>
      
      <!-- 学习统计 -->
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{learnData.totalWords}}</text>
          <text class="stat-label">已学字词</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{learnData.learnDays}}</text>
          <text class="stat-label">学习天数</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 - 网格布局 -->
    <view class="action-buttons-grid">
      <!-- 第一行按钮 -->
      <view class="button-row">
        <!-- 开始学习按钮 -->
        <button class="btn btn-card btn-primary" bindtap="startLearning">
          <view class="btn-content">
            <view class="btn-icon-large">
              <text class="icon-text">▶️</text>
            </view>
            <view class="btn-text-wrapper">
              <text class="btn-text-main">开始学习</text>
            </view>
          </view>
        </button>
        
        <!-- 复习模式按钮 -->
        <button class="btn btn-card btn-secondary" bindtap="startReview">
          <view class="btn-content">
            <view class="btn-icon-large">
              <text class="icon-text">🔄</text>
            </view>
            <view class="btn-text-wrapper">
              <text class="btn-text-main">复习模式</text>
            </view>
          </view>
        </button>
      </view>
      
      <!-- 第二行按钮 -->
      <view class="button-row">
        <!-- 回顾模式按钮 -->
        <button class="btn btn-card btn-secondary" bindtap="startRecall">
          <view class="btn-content">
            <view class="btn-icon-large">
              <text class="icon-text">📝</text>
            </view>
            <view class="btn-text-wrapper">
              <text class="btn-text-main">回顾模式</text>
            </view>
          </view>
        </button>
        
        <!-- 查看记录按钮 -->
        <button class="btn btn-card btn-outline" bindtap="viewProgress">
          <view class="btn-content">
            <view class="btn-icon-large">
              <text class="icon-text">📊</text>
            </view>
            <view class="btn-text-wrapper">
              <text class="btn-text-main">查看记录</text>
            </view>
          </view>
        </button>
      </view>
    </view>
  </view>
</view>
