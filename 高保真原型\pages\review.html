<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复习模式</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            backdrop-filter: blur(10px);
        }
        
        .header-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .progress-info {
            text-align: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px - 80px - 120px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .review-card {
            background: white;
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            width: 100%;
            max-width: 300px;
        }
        
        .word-display {
            font-size: 72px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .question-text {
            font-size: 20px;
            color: #666;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .choice-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .choice-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            font-size: 32px;
            font-weight: bold;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .choice-btn.know {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .choice-btn.unknown {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        
        .choice-btn:active {
            transform: scale(0.95);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: white;
            width: 50%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .instruction-text {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            color: white;
            text-align: center;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .instruction-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #fa709a;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #fa709a;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
        
        .stats-display {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            color: white;
            font-size: 14px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 页面头部 -->
    <div class="header">
        <div class="header-left">
            <div class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="header-title">复习模式</div>
        </div>
        <div class="progress-info">3 / 6</div>
    </div>
    
    <!-- 进度条和统计 -->
    <div style="padding: 0 20px;">
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        <div class="stats-display">
            <div class="stat-item">
                <div class="stat-number">2</div>
                <div class="stat-label">认识</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1</div>
                <div class="stat-label">不认识</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">剩余</div>
            </div>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 复习卡片 -->
        <div class="review-card">
            <div class="word-display">香蕉</div>
            <div class="question-text">你认识这个字词吗？</div>
            <div class="choice-buttons">
                <button class="choice-btn know">
                    <i class="fas fa-check"></i>
                </button>
                <button class="choice-btn unknown">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <!-- 说明文字 -->
        <div class="instruction-text">
            <div class="instruction-title">
                <i class="fas fa-info-circle"></i>
                复习说明
            </div>
            让孩子看字词，判断是否认识<br>
            <span style="color: #4CAF50;">✓ 认识</span> 或 
            <span style="color: #f44336;">✗ 不认识</span>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item inactive">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
