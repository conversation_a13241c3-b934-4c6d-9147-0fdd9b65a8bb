/**
 * 完成页面样式
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  height: 100vh;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.celebration-section {
  text-align: center;
  position: relative;
}

/* 彩带动画 */
.confetti {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;
}

.confetti-item {
  position: absolute;
  font-size: 60rpx;
  animation: confetti-fall 3s infinite;
}

.confetti-item:nth-child(1) { left: 10%; }
.confetti-item:nth-child(2) { left: 30%; }
.confetti-item:nth-child(3) { left: 50%; }
.confetti-item:nth-child(4) { left: 70%; }
.confetti-item:nth-child(5) { left: 90%; }

@keyframes confetti-fall {
  0% {
    transform: translateY(-100rpx) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(400rpx) rotate(360deg);
    opacity: 0;
  }
}

/* 成就徽章 */
.achievement-badge {
  margin: 100rpx 0 60rpx;
}

.badge-ring {
  width: 200rpx;
  height: 200rpx;
  border: 8rpx solid rgba(255,255,255,0.3);
  border-radius: 50%;
  margin: 0 auto 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: badge-pulse 2s infinite;
}

.badge-inner {
  width: 160rpx;
  height: 160rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.badge-icon {
  font-size: 80rpx;
}

@keyframes badge-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.badge-text {
  color: white;
}

.badge-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.badge-subtitle {
  display: block;
  font-size: 32rpx;
  opacity: 0.9;
}

/* 学习统计 */
.stats-summary {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.summary-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.summary-item {
  text-align: center;
  color: white;
}

.summary-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.summary-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.accuracy-rate {
  color: white;
  text-align: center;
}

.accuracy-text {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.accuracy-bar {
  height: 12rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 6rpx;
  overflow: hidden;
}

.accuracy-fill {
  height: 100%;
  background: white;
  border-radius: 6rpx;
  transition: width 1s ease;
}

/* 鼓励话语 */
.encouragement {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
}

.encouragement-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.encouragement-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.encouragement-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 30rpx;
}

.btn-secondary {
  flex: 1;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}

.btn-primary {
  flex: 2;
  background: white;
  color: #fa709a;
}

.icon-text {
  font-size: inherit;
}
