/**
 * 回顾模式页面样式
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.progress-section {
  padding: 30rpx 40rpx;
  color: white;
}

.progress-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.progress-text {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.progress-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.main-content {
  height: calc(100vh - 140rpx - 160rpx);
  padding: 0 40rpx;
  overflow-y: auto;
}

.word-display-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.word-card {
  background: white;
  border-radius: 40rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
  position: relative;
}

.difficulty-badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.word-text {
  display: block;
  font-size: 100rpx;
  font-weight: bold;
  color: #a8edea;
  margin-bottom: 20rpx;
  text-shadow: 0 4rpx 8rpx rgba(168, 237, 234, 0.3);
}

.word-pinyin {
  font-size: 32rpx;
  color: #666;
  opacity: 0.8;
}

.memory-tips, .practice-guide {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.tips-title, .guide-title {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tips-content {
  color: white;
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.tip-item {
  display: block;
  margin-bottom: 8rpx;
}

.guide-steps {
  color: white;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 24rpx;
  flex-shrink: 0;
}

.step-text {
  flex: 1;
  opacity: 0.9;
}

.action-buttons {
  padding: 40rpx;
  display: flex;
  gap: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.btn-secondary {
  flex: 1;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}

.btn-secondary[disabled] {
  background: rgba(255,255,255,0.1);
  color: rgba(255,255,255,0.5);
  border-color: rgba(255,255,255,0.2);
}

.btn-primary {
  flex: 2;
  background: white;
  color: #a8edea;
}

.icon-text {
  font-size: inherit;
}
