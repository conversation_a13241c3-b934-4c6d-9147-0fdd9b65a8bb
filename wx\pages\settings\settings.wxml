<!--
  设置页面
  功能：个人信息和应用设置
-->
<view class="container gradient-bg-blue">

  
  <!-- 主要内容 -->
  <scroll-view class="main-content" scroll-y="true">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-card glass-card">
        <view class="user-avatar">
          <text class="avatar-icon">👶</text>
        </view>
        <view class="user-info">
          <text class="user-name">{{userInfo.name || '小朋友'}}</text>
          <text class="user-age">{{userInfo.age || '3-8岁'}}</text>
        </view>
        <button class="edit-btn" bindtap="editUserInfo">
          <text>编辑</text>
        </button>
      </view>
    </view>
    
    <!-- 学习设置 -->
    <view class="settings-section">
      <view class="section-title">
        <text class="icon-text">⚙️</text>
        <text>学习设置</text>
      </view>
      
      <view class="settings-group card">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">音效开关</text>
            <text class="setting-desc">开启学习过程中的音效提示</text>
          </view>
          <switch 
            checked="{{settings.soundEnabled}}" 
            bindchange="onSoundToggle"
            color="#667eea"
          />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">自动下一个</text>
            <text class="setting-desc">学习时自动切换到下一个字词</text>
          </view>
          <switch 
            checked="{{settings.autoNext}}" 
            bindchange="onAutoNextToggle"
            color="#667eea"
          />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">学习难度</text>
            <text class="setting-desc">当前：{{difficultyText}}</text>
          </view>
          <button class="setting-btn" bindtap="changeDifficulty">
            <text>{{difficultyText}}</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 数据管理 -->
    <view class="data-section">
      <view class="section-title">
        <text class="icon-text">💾</text>
        <text>数据管理</text>
      </view>
      
      <view class="data-group card">
        <view class="data-item" bindtap="exportData">
          <view class="data-icon">📤</view>
          <view class="data-info">
            <text class="data-title">导出学习数据</text>
            <text class="data-desc">将学习记录导出到本地</text>
          </view>
          <text class="data-arrow">></text>
        </view>
        
        <view class="data-item" bindtap="importData">
          <view class="data-icon">📥</view>
          <view class="data-info">
            <text class="data-title">导入学习数据</text>
            <text class="data-desc">从本地文件导入学习记录</text>
          </view>
          <text class="data-arrow">></text>
        </view>
        
        <view class="data-item" bindtap="clearData">
          <view class="data-icon">🗑️</view>
          <view class="data-info">
            <text class="data-title">清空学习数据</text>
            <text class="data-desc">删除所有学习记录（不可恢复）</text>
          </view>
          <text class="data-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 关于应用 -->
    <view class="about-section">
      <view class="section-title">
        <text class="icon-text">ℹ️</text>
        <text>关于应用</text>
      </view>
      
      <view class="about-group card">
        <view class="about-item">
          <text class="about-label">应用版本</text>
          <text class="about-value">v1.0.0</text>
        </view>
        
        <view class="about-item">
          <text class="about-label">开发者</text>
          <text class="about-value">儿童教育团队</text>
        </view>
        
        <view class="about-item" bindtap="showPrivacy">
          <text class="about-label">隐私政策</text>
          <text class="about-arrow">></text>
        </view>
        
        <view class="about-item" bindtap="showTerms">
          <text class="about-label">使用条款</text>
          <text class="about-arrow">></text>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
