/**
 * 学习模式页面样式
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.progress-section {
  padding: 30rpx 40rpx;
  color: white;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.progress-word {
  font-size: 32rpx;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.main-content {
  height: calc(100vh - 140rpx - 160rpx);
  padding: 0 40rpx;
  overflow-y: auto;
}

.word-display-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.word-card {
  background: white;
  border-radius: 40rpx;
  padding: 80rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.word-text {
  display: block;
  font-size: 120rpx;
  font-weight: bold;
  color: #000000;
  margin-bottom: 20rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.word-pinyin {
  font-size: 36rpx;
  color: #666;
  opacity: 0.8;
}

.image-section {
  margin-bottom: 40rpx;
}

.image-placeholder {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  color: white;
  border: 4rpx dashed rgba(255,255,255,0.5);
}

.placeholder-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.placeholder-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.placeholder-hint {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.learning-tips {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
}

.tips-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tips-content {
  color: white;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.6;
}

.tip-item {
  display: block;
  margin-bottom: 10rpx;
}

.action-buttons {
  padding: 40rpx;
  display: flex;
  gap: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.btn-secondary {
  flex: 1;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 4rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20rpx);
}

.btn-secondary[disabled] {
  background: rgba(255,255,255,0.1);
  color: rgba(255,255,255,0.5);
  border-color: rgba(255,255,255,0.2);
}

.btn-primary {
  flex: 1;
  background: white;
  color: #4facfe;
}

.icon-text {
  font-size: inherit;
}
