/**
 * 字词输入页面逻辑
 * 功能：输入今日要学习的字词（仅批量输入）
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    batchInputText: '', // 批量输入文本
    previewWords: [], // 预览字词列表
    newWords: [], // 新字词列表
    learnedWords: [], // 已学过的字词列表
    canStart: false // 是否可以开始学习
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('字词输入页加载')
    this.loadTodayWords()
  },

  /**
   * 加载今日字词
   */
  loadTodayWords: function() {
    try {
      const learnData = app.getLearnData()
      if (learnData.todayWords && learnData.todayWords.length > 0) {
        const words = learnData.todayWords
        
        this.setData({
          batchInputText: words.join('，'),
          previewWords: words,
          canStart: words.length > 0
        })
        
        // 分类字词
        this.classifyWords(words)
      }
    } catch (error) {
      console.error('加载今日字词失败:', error)
    }
  },

  /**
   * 批量输入事件
   */
  onBatchInput: function(e) {
    const value = e.detail.value
    this.setData({
      batchInputText: value
    })
  },

  /**
   * 应用批量输入
   */
  applyBatchInput: function() {
    const text = this.data.batchInputText.trim()
    if (!text) {
      this.setData({
        previewWords: [],
        newWords: [],
        learnedWords: [],
        canStart: false
      })
      return
    }

    // 按照常见分隔符（逗号、空格、换行等）分割字词
    const words = this.splitTextToWords(text)
    
    // 分类字词为新字词和已学过的字词
    this.classifyWords(words)
    
    wx.showToast({
      title: '已确认输入',
      icon: 'success'
    })
  },

  /**
   * 将字词分类为新字词和已学过的字词
   */
  classifyWords: function(words) {
    if (!words || words.length === 0) {
      this.setData({
        previewWords: [],
        newWords: [],
        learnedWords: [],
        canStart: false
      })
      return
    }
    
    try {
      const learnData = app.getLearnData()
      const existingWords = learnData.learnedWords || []
      
      // 过滤出新字词和已学过的字词
      const newWords = []
      const learnedWords = []
      
      words.forEach(word => {
        if (existingWords.includes(word)) {
          learnedWords.push(word)
        } else {
          newWords.push(word)
        }
      })
      
      this.setData({
        previewWords: words,
        newWords: newWords,
        learnedWords: learnedWords,
        canStart: words.length > 0
      })
      
      // 显示分类结果
      if (learnedWords.length > 0) {
        wx.showModal({
          title: '字词分类结果',
          content: `共输入${words.length}个字词，其中${newWords.length}个新字词，${learnedWords.length}个已学过的字词。`,
          showCancel: false
        })
      }
    } catch (error) {
      console.error('字词分类失败:', error)
      this.setData({
        previewWords: words,
        newWords: words,
        learnedWords: [],
        canStart: words.length > 0
      })
    }
  },

  /**
   * 将文本按照分隔符分割成字词数组
   */
  splitTextToWords: function(text) {
    if (!text) return []
    
    // 替换常见的分隔符为统一的分隔符
    const normalizedText = text
      .replace(/[,，;；、\s\n\r]+/g, ',')
      .replace(/^,+|,+$/g, '') // 去除首尾的分隔符
    
    // 分割并过滤空字符串
    return normalizedText.split(',').map(word => word.trim()).filter(word => word)
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack({
      delta: 1,
      success: () => {
        console.log('返回成功')
      },
      fail: (error) => {
        console.error('返回失败:', error)
        // 如果返回失败，跳转到首页
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    })
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    if (!this.data.canStart) {
      wx.showToast({
        title: '请至少输入1个字词',
        icon: 'none'
      })
      return
    }

    const words = this.data.previewWords
    
    if (words.length === 0) {
      wx.showToast({
        title: '请输入要学习的字词',
        icon: 'none'
      })
      return
    }

    // 保存今日字词
    this.saveTodayWords(words)

    // 跳转到学习页面
    wx.navigateTo({
      url: '/pages/learn/learn',
      success: () => {
        console.log('跳转到学习页面成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 保存今日字词
   */
  saveTodayWords: function(words) {
    try {
      const learnData = app.getLearnData()
      learnData.todayWords = words
      
      // 只将新字词添加到learnedWords数组中
      if (this.data.newWords && this.data.newWords.length > 0) {
        // 更新总字词数（去重）
        const allWords = [...(learnData.learnedWords || []), ...this.data.newWords]
        const uniqueWords = [...new Set(allWords)]
        learnData.learnedWords = uniqueWords
        learnData.totalWords = uniqueWords.length
      }
      
      app.saveLearnData(learnData)
      
      console.log('保存字词成功，总字词数:', learnData.totalWords)
      console.log('今日学习字词:', words)
      console.log('其中新字词:', this.data.newWords)
      
      wx.showToast({
        title: '字词保存成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存字词失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadTodayWords()
    wx.stopPullDownRefresh()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '儿童识字 - 今日学习准备',
      path: '/pages/input/input'
    }
  }
})
