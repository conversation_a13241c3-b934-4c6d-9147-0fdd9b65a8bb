<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字词输入</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            /* 禁止左右滑动，强制宽度自适应 */
            width: 100vw;
            max-width: 100vw;
            overflow-x: hidden;
        }
        /* 美化滚动条样式 */
        .main-content::-webkit-scrollbar {
            width: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
        }
        .main-content::-webkit-scrollbar-thumb {
            background: #f093fb;
            border-radius: 8px;
        }
        .main-content::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        /* Firefox 滚动条美化 */
        .main-content {
            scrollbar-width: thin;
            scrollbar-color: #f093fb rgba(255,255,255,0.2);
        }
        /* 底部按钮区域增加留白 */
        .action-buttons {
            padding: 20px 20px 32px 20px; /* 底部多留白 */
            display: flex;
            gap: 15px;
        }
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
            padding-bottom: 32px; /* 再次增大底部留白 */
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px - 100px);
            padding: 0 20px;
            overflow-y: auto;
        }
        
        .input-container {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .input-grid {
            display: grid;
            gap: 15px;
        }
        
        .input-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .input-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .input-field {
            flex: 1;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 15px;
            font-size: 18px;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #f093fb;
            background: white;
            box-shadow: 0 0 0 3px rgba(240, 147, 251, 0.1);
        }
        
        .input-field::placeholder {
            color: #999;
            font-size: 16px;
        }
        
        .tips-section {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .tips-title {
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tips-list {
            color: white;
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .tips-list li {
            margin-bottom: 5px;
        }
        
        .action-buttons {
            padding: 20px;
            display: flex;
            gap: 15px;
        }
        
        .btn-secondary {
            flex: 1;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }
        
        .btn-primary {
            flex: 2;
            background: white;
            color: #f5576c;
            border: none;
            border-radius: 25px;
            padding: 18px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #f5576c;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #f5576c;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 页面头部 -->
    <div class="header">
        <h1>今日学习准备</h1>
        <p>请输入今天要学习的6个字词</p>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 输入区域 -->
        <div class="input-container">
            <div class="input-grid">
                <div class="input-item">
                    <div class="input-number">1</div>
                    <input type="text" class="input-field" placeholder="请输入第1个字词" maxlength="4">
                </div>
                <div class="input-item">
                    <div class="input-number">2</div>
                    <input type="text" class="input-field" placeholder="请输入第2个字词" maxlength="4">
                </div>
                <div class="input-item">
                    <div class="input-number">3</div>
                    <input type="text" class="input-field" placeholder="请输入第3个字词" maxlength="4">
                </div>
                <div class="input-item">
                    <div class="input-number">4</div>
                    <input type="text" class="input-field" placeholder="请输入第4个字词" maxlength="4">
                </div>
                <div class="input-item">
                    <div class="input-number">5</div>
                    <input type="text" class="input-field" placeholder="请输入第5个字词" maxlength="4">
                </div>
                <div class="input-item">
                    <div class="input-number">6</div>
                    <input type="text" class="input-field" placeholder="请输入第6个字词" maxlength="4">
                </div>
            </div>
        </div>
        
        <!-- 提示区域 -->
        <div class="tips-section">
            <div class="tips-title">
                <i class="fas fa-lightbulb"></i>
                学习小贴士
            </div>
            <ul class="tips-list">
                <li>建议选择孩子感兴趣的字词</li>
                <li>可以是生活中常见的物品名称</li>
                <li>每个字词建议不超过4个字</li>
                <li>可以重复之前学过的字词加深印象</li>
            </ul>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="action-buttons">
        <button class="btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            返回
        </button>
        <button class="btn-primary">
            <i class="fas fa-play mr-2"></i>
            开始学习
        </button>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item inactive">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
