<!--
  完成页面
  功能：学习结束庆祝
-->
<view class="container gradient-bg-orange">

  
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 庆祝动画区域 -->
    <view class="celebration-section">
      <!-- 彩带装饰 -->
      <view class="confetti">
        <text class="confetti-item" style="animation-delay: 0s;">🎉</text>
        <text class="confetti-item" style="animation-delay: 0.2s;">🎊</text>
        <text class="confetti-item" style="animation-delay: 0.4s;">⭐</text>
        <text class="confetti-item" style="animation-delay: 0.6s;">🌟</text>
        <text class="confetti-item" style="animation-delay: 0.8s;">✨</text>
      </view>
      
      <!-- 成就徽章 -->
      <view class="achievement-badge">
        <view class="badge-ring">
          <view class="badge-inner">
            <text class="badge-icon">🏆</text>
          </view>
        </view>
        <view class="badge-text">
          <text class="badge-title">学习完成！</text>
          <text class="badge-subtitle">今日任务达成</text>
        </view>
      </view>
      
      <!-- 学习统计 -->
      <view class="stats-summary glass-card">
        <view class="summary-title">
          <text class="icon-text">📊</text>
          <text>今日学习总结</text>
        </view>
        <view class="summary-stats">
          <view class="summary-item">
            <text class="summary-number">{{todayStats.totalWords}}</text>
            <text class="summary-label">学习字词</text>
          </view>
          <view class="summary-item">
            <text class="summary-number">{{todayStats.knownWords}}</text>
            <text class="summary-label">已掌握</text>
          </view>
          <view class="summary-item">
            <text class="summary-number">{{todayStats.reviewWords}}</text>
            <text class="summary-label">需复习</text>
          </view>
        </view>
        <view class="accuracy-rate">
          <text class="accuracy-text">掌握率：{{todayStats.accuracy}}%</text>
          <view class="accuracy-bar">
            <view class="accuracy-fill" style="width: {{todayStats.accuracy}}%"></view>
          </view>
        </view>
      </view>
      
      <!-- 鼓励话语 -->
      <view class="encouragement glass-card">
        <view class="encouragement-icon">{{encouragementData.icon}}</view>
        <text class="encouragement-text">{{encouragementData.text}}</text>
        <text class="encouragement-subtitle">{{encouragementData.subtitle}}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" bindtap="viewProgress">
        <text class="icon-text">📈</text>
        <text>查看进度</text>
      </button>
      <button class="btn btn-primary" bindtap="backToHome">
        <text class="icon-text">🏠</text>
        <text>返回首页</text>
      </button>
    </view>
  </view>
</view>
