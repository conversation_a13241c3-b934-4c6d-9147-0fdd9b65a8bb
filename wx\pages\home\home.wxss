/**
 * 首页样式
 * 基于原HTML设计，适配微信小程序
 */

.container {
  height: 100vh;
  overflow: hidden;
}

.main-content {
  height: 100vh;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.welcome-section {
  text-align: center;
  color: white;
}

.app-icon {
  width: 160rpx;
  height: 160rpx;
  background: white;
  border-radius: 40rpx;
  margin: 0 auto 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.icon-text {
  font-size: inherit;
}

.welcome-text {
  margin-bottom: 60rpx;
}

.welcome-title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  display: block;
  font-size: 32rpx;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.stat-card {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 网格布局按钮组 */
.action-buttons-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
}

/* 按钮行 */
.button-row {
  display: flex;
  gap: 20rpx;
  width: 100%;
}

/* 卡片式按钮基础样式 */
.btn-card {
  flex: 1;
  height: 200rpx;
  border-radius: 30rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 按钮内容布局 */
.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20rpx;
}

/* 主按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
  color: #5468c9;
  box-shadow: 0 15rpx 30rpx rgba(102, 126, 234, 0.3);
  transform: translateY(0);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 50%);
  border-radius: 30rpx;
  z-index: 0;
}

.btn-primary:active {
  transform: translateY(6rpx);
  box-shadow: 0 8rpx 15rpx rgba(102, 126, 234, 0.2);
}

/* 次要按钮样式 */
.btn-secondary {
  background: rgba(255,255,255,0.25);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.5);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.1);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.btn-secondary:active {
  transform: translateY(6rpx);
  box-shadow: 0 5rpx 10rpx rgba(0,0,0,0.08);
  background: rgba(255,255,255,0.3);
}

/* 按钮内部元素样式 */
.btn-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 40rpx;
}

/* 大图标样式 - 用于卡片按钮 */
.btn-icon-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  font-size: 50rpx;
  margin-bottom: 16rpx;
}

.btn-text-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center; /* 居中对齐文字 */
}

.btn-text-main {
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-align: center; /* 文字居中 */
}

/* 主按钮特殊样式 */
.btn-primary .btn-text-main {
  color: #5468c9;
  font-size: 36rpx;
}

/* 次按钮特殊样式 */
.btn-secondary .btn-text-main {
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

/* 轮廓按钮样式 */
.btn-outline {
  background: transparent;
  border: 2rpx solid rgba(255,255,255,0.6);
  box-shadow: none;
}

.btn-outline:active {
  background: rgba(255,255,255,0.15);
  transform: translateY(4rpx);
}

/* 卡片按钮的悬浮效果 */
.btn-card:active {
  transform: scale(0.98);
  transition: transform 0.2s ease;
}
