/**
 * 首页逻辑
 * 功能：展示学习统计，提供学习入口
 */
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    totalWords: 0, // 总字词数
    learnDays: 0, // 学习天数
    todayCompleted: false, // 今日是否已完成学习
    lastLearnDate: '', // 最后学习日期
    userInfo: null, // 用户信息
    hasUserInfo: false, // 是否已获取用户信息
    canIUseGetUserProfile: false, // 是否可以使用getUserProfile
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('首页加载')
    
    // 检查是否可以使用getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    
    // 加载学习数据
    this.loadLearnData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时重新加载数据
    this.loadLearnData()
  },

  /**
   * 加载学习数据
   */
  loadLearnData: function() {
    try {
      const learnData = app.getLearnData()
      
      this.setData({
        totalWords: learnData.totalWords || 0,
        learnDays: learnData.learnDays || 0,
        todayCompleted: learnData.todayCompleted || false,
        lastLearnDate: learnData.lastLearnDate || ''
      })
    } catch (error) {
      console.error('加载学习数据失败:', error)
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      })
    }
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    console.log('开始学习')
    
    // 检查今日是否已完成学习
    const todayCompleted = wx.getStorageSync('todayCompleted')
    if (todayCompleted) {
      wx.showModal({
        title: '提示',
        content: '今日学习已完成，是否重新开始？',
        success: (res) => {
          if (res.confirm) {
            this.navigateToInput()
          }
        }
      })
    } else {
      this.navigateToInput()
    }
  },

  /**
   * 跳转到字词输入页
   */
  navigateToInput: function() {
    // 使用绝对路径，确保路径正确
    const url = '/pages/input/input'
    
    console.log('准备跳转到:', url)
    
    wx.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到字词输入页成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        
        // 详细记录错误信息
        console.error('错误详情:', JSON.stringify(error))
        
        // 尝试使用switchTab作为备选方案
        if (error.errMsg && error.errMsg.indexOf('tabbar') >= 0) {
          console.log('尝试使用switchTab跳转')
          wx.switchTab({
            url: url,
            fail: (err) => {
              console.error('switchTab也失败了:', err)
              wx.showToast({
                title: '跳转失败，请重试',
                icon: 'none'
              })
            }
          })
        } else {
          // 显示更详细的错误提示
          wx.showModal({
            title: '跳转失败',
            content: '无法跳转到输入页面，请检查网络或重启小程序。\n错误信息: ' + (error.errMsg || '未知错误'),
            showCancel: false
          })
        }
      }
    })
  },

  /**
   * 查看学习记录
   */
  viewProgress: function() {
    wx.navigateTo({
      url: '/pages/progress/progress',
      fail: (error) => {
        console.error('跳转到进度页失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 开始复习
   */
  startReview: function() {
    const learnData = app.getLearnData()
    const learnedWords = learnData.learnedWords || []
    
    if (learnedWords.length === 0) {
      wx.showToast({
        title: '暂无可复习的字词',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/review/review?fromHome=true',
      fail: (error) => {
        console.error('跳转到复习页失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 开始回顾
   */
  startRecall: function() {
    const learnData = app.getLearnData()
    const learnedWords = learnData.learnedWords || []
    
    if (learnedWords.length === 0) {
      wx.showToast({
        title: '暂无可回顾的字词',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/recall/recall?fromHome=true',
      fail: (error) => {
        console.error('跳转到回顾页失败:', error)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 获取用户信息
   */
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadLearnData()
    wx.stopPullDownRefresh()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '儿童识字 - 快乐学习每一天',
      path: '/pages/home/<USER>'
    }
  }
})
