<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习完成</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
            text-align: center;
        }
        
        .celebration-icon {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: #fcb69f;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .congratulations {
            color: white;
            margin-bottom: 40px;
        }
        
        .congrats-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .congrats-subtitle {
            font-size: 18px;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .results-card {
            background: white;
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 300px;
        }
        
        .results-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-number.learned {
            color: #4CAF50;
        }
        
        .stat-number.review {
            color: #ff9800;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .progress-ring {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            position: relative;
        }
        
        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }
        
        .progress-ring .bg {
            stroke: #f0f0f0;
        }
        
        .progress-ring .progress {
            stroke: #4CAF50;
            stroke-linecap: round;
            stroke-dasharray: 188.5;
            stroke-dashoffset: 47.1;
            transition: stroke-dashoffset 1s ease;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .mastery-rate {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .encouragement {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px;
            border-radius: 15px;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 100%;
            max-width: 300px;
        }
        
        .btn-primary {
            background: white;
            color: #fcb69f;
            border: none;
            border-radius: 25px;
            padding: 18px 30px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 16px 30px;
            font-size: 16px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #fcb69f;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #fcb69f;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ff6b6b;
            animation: confetti-fall 3s linear infinite;
        }
        
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
        
        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #ff6b6b; }
        .confetti:nth-child(2) { left: 20%; animation-delay: 0.5s; background: #4ecdc4; }
        .confetti:nth-child(3) { left: 30%; animation-delay: 1s; background: #45b7d1; }
        .confetti:nth-child(4) { left: 40%; animation-delay: 1.5s; background: #f9ca24; }
        .confetti:nth-child(5) { left: 50%; animation-delay: 2s; background: #6c5ce7; }
        .confetti:nth-child(6) { left: 60%; animation-delay: 0.3s; background: #a29bfe; }
        .confetti:nth-child(7) { left: 70%; animation-delay: 0.8s; background: #fd79a8; }
        .confetti:nth-child(8) { left: 80%; animation-delay: 1.3s; background: #00b894; }
        .confetti:nth-child(9) { left: 90%; animation-delay: 1.8s; background: #e17055; }
    </style>
</head>
<body>
    <!-- 彩带效果 -->
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 庆祝图标 -->
        <div class="celebration-icon">
            🎉
        </div>
        
        <!-- 祝贺文字 -->
        <div class="congratulations">
            <div class="congrats-title">太棒了！</div>
            <div class="congrats-subtitle">今天的学习任务完成啦！<br>你真是个爱学习的好孩子</div>
        </div>
        
        <!-- 学习结果 -->
        <div class="results-card">
            <div class="results-title">
                <i class="fas fa-chart-bar"></i>
                今日学习成果
            </div>
            
            <div class="mastery-rate">
                <div class="progress-ring">
                    <svg>
                        <circle class="bg" cx="40" cy="40" r="30"></circle>
                        <circle class="progress" cx="40" cy="40" r="30"></circle>
                    </svg>
                    <div class="progress-text">75%</div>
                </div>
                <div style="font-size: 14px; color: #666;">掌握率</div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number learned">4</div>
                    <div class="stat-label">已掌握</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number review">2</div>
                    <div class="stat-label">需复习</div>
                </div>
            </div>
            
            <div class="encouragement">
                <i class="fas fa-star mr-2"></i>
                <strong>继续加油！</strong> 明天再来学习新的字词吧，每天进步一点点，你会越来越棒的！
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn-primary">
                <i class="fas fa-home"></i>
                返回首页
            </button>
            <button class="btn-secondary">
                <i class="fas fa-chart-line"></i>
                查看学习记录
            </button>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item inactive">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
