<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 47px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .main-content {
            height: calc(100vh - 47px - 80px);
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .welcome-section {
            text-align: center;
            color: white;
        }
        
        .app-icon {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: #667eea;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .welcome-text h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .welcome-text p {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .primary-btn {
            background: white;
            color: #667eea;
            border: none;
            border-radius: 25px;
            padding: 18px 30px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .secondary-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 16px 30px;
            font-size: 16px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }
        
        .bottom-nav {
            height: 80px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #667eea;
            font-size: 12px;
            font-weight: 600;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-item.inactive {
            color: #999;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div style="display: flex; gap: 5px;">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <div class="app-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="welcome-text">
                <h1>儿童识字</h1>
                <p>让学习变得简单有趣</p>
            </div>
            
            <!-- 学习统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div class="stat-label">已学字词</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">学习天数</div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="primary-btn">
                <i class="fas fa-play mr-2"></i>
                开始今日学习
            </button>
            <button class="secondary-btn">
                <i class="fas fa-history mr-2"></i>
                查看学习记录
            </button>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-book"></i>
            <span>学习</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-chart-line"></i>
            <span>进度</span>
        </div>
        <div class="nav-item inactive">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
