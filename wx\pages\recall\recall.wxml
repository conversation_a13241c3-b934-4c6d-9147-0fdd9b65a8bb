<!--
  回顾模式页面
  功能：重点复习不认识的字词
-->
<view class="container gradient-bg-purple">

  
  <!-- 进度条 -->
  <view class="progress-section">
    <view class="progress-info">
      <text class="progress-text">回顾进度 {{currentIndex + 1}}/{{totalWords}}</text>
      <text class="progress-label">重点练习不认识的字词</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progressPercent}}%"></view>
    </view>
  </view>
  
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 字词展示区域 -->
    <view class="word-display-section">
      <view class="word-card">
        <view class="difficulty-badge">
          <text>🎯 重点练习</text>
        </view>
        <text class="word-text">{{currentWord}}</text>
        <view class="word-pinyin" wx:if="{{currentPinyin}}">
          <text>{{currentPinyin}}</text>
        </view>
      </view>
      
      <!-- 记忆提示区域 -->
      <view class="memory-tips glass-card">
        <view class="tips-title">
          <text class="icon-text">🧠</text>
          <text>记忆小贴士</text>
        </view>
        <view class="tips-content">
          <text class="tip-item">• 仔细观察字词的结构和特点</text>
          <text class="tip-item">• 联想生活中的相关事物</text>
          <text class="tip-item">• 重复朗读加深印象</text>
          <text class="tip-item">• 尝试用字词造句</text>
        </view>
      </view>
    </view>
    
    <!-- 练习指导 -->
    <view class="practice-guide glass-card">
      <view class="guide-title">
        <text class="icon-text">👨‍🏫</text>
        <text>练习指导</text>
      </view>
      <view class="guide-steps">
        <view class="step-item">
          <text class="step-number">1</text>
          <text class="step-text">家长带读字词3遍</text>
        </view>
        <view class="step-item">
          <text class="step-number">2</text>
          <text class="step-text">孩子跟读字词3遍</text>
        </view>
        <view class="step-item">
          <text class="step-number">3</text>
          <text class="step-text">解释字词含义</text>
        </view>
        <view class="step-item">
          <text class="step-number">4</text>
          <text class="step-text">让孩子独立读出</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn btn-secondary" bindtap="previousWord" disabled="{{currentIndex === 0}}">
      <text class="icon-text">⬅️</text>
      <text>上一个</text>
    </button>
    <button class="btn btn-primary" bindtap="nextWord">
      <text class="icon-text">{{isLastWord ? '🎉' : '➡️'}}</text>
      <text>{{isLastWord ? '完成回顾' : '下一个'}}</text>
    </button>
  </view>
</view>
