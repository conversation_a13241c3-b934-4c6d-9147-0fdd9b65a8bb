<!-- 字词输入页面 -->
<view class="container">
  <view class="header">
    <text class="title">今日学习准备</text>
  </view>
  
  <view class="content">
    <!-- 批量输入区域 -->
    <view class="batch-input-section">
      <text class="section-title">批量输入字词</text>
      <textarea 
        class="batch-input" 
        placeholder="请输入要学习的字词，用逗号、空格或换行分隔" 
        value="{{batchInputText}}" 
        bindinput="onBatchInput"
        maxlength="-1"
      ></textarea>
      <button class="apply-btn" bindtap="applyBatchInput">确认输入</button>
    </view>
    
    <!-- 字词预览区域 -->
    <view class="preview-section" wx:if="{{previewWords.length > 0}}">
      <text class="section-title">字词预览 ({{previewWords.length}}个)</text>
      <view class="preview-words">
        <block wx:for="{{previewWords}}" wx:key="index">
          <view class="word-item {{newWords.includes(item) ? 'new-word' : 'learned-word'}}">
            {{item}}
            <text class="word-tag" wx:if="{{learnedWords.includes(item)}}">已学</text>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 提示区域 -->
    <view class="tips-section">
      <text class="tips-title">小提示</text>
      <text class="tips-content">1. 可以一次输入多个字词，用逗号、空格或换行分隔</text>
      <text class="tips-content">2. 已学过的字词会被标记出来</text>
      <text class="tips-content">3. 点击"开始学习"后将进入学习模式</text>
    </view>
  </view>
  
  <view class="footer">
    <button class="back-btn" bindtap="goBack">返回</button>
    <button class="start-btn {{!canStart ? 'disabled' : ''}}" bindtap="startLearning" disabled="{{!canStart}}">开始学习</button>
  </view>
</view>
