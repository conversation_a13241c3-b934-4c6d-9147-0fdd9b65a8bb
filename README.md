# 项目开发
重要约定：
- 代码都在`wx`目录下，仅仅改动这个目录下的文件即可。

##  待办事项
- [x] 字词输入优化：目前字词输入是逐个输入的，改成从一段文本中按照常见的分隔符自动切分，比如“鸥，哼，推，浇，流，彤”，能自动切分为“鸥”，“哼”，“推”，“浇”，“流”，“彤”。
- [x] 学习模式页面优化：字词颜色设置为黑色；去掉“图片”展示区域；修正上一个显示换行的问题；
- [x] 首页优化：增加两个功能入口：复习模式、回顾模式；
- [x] 复习模式优化，如果是从首页跳入，就复习已经学习过的字词，从近到远；
- [x] 回顾模式优化，如果是从首页跳入，就复习已经学习过的字词，从远到近；
- [x] 学习页面优化：取消6个字词的限制，改为输入多少字词就学习多少字词；
- [x] 删除单个输入区域，只保留批量输入的方式
- [ ] 学习页面优化：确认输入时过滤已经学习过的字词
- [ ] 首页优化：点击开始学习，弹出“跳转失败”，应该跳转到学习页面
