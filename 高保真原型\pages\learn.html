<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>学习模式 - Tailwind恢复测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script> <!-- Re-enabled Tailwind -->
    <style>
        html {
            box-sizing: border-box;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            font-family: 'Arial', sans-serif;
            /* background: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%); */ /* Tailwind will handle bg */
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative; /* Keep for now */
            padding-bottom: 80px; /* Add padding to prevent content overlap with fixed nav */
        }
        /* Minimal custom styles, relying more on Tailwind now */
        .status-bar {
            /* Tailwind: w-full px-5 py-2.5 flex justify-between items-center text-white bg-black bg-opacity-10 */
        }
        .header {
            /* Tailwind: w-full px-5 py-3.5 flex items-center text-white */
        }
        .back-btn {
            /* Tailwind: text-2xl text-white no-underline mr-4 */
        }
        .header h1 {
            /* Tailwind: text-xl font-semibold m-0 */
        }
        .main-content {
            /* Tailwind: flex-grow w-full max-w-md p-5 mx-auto overflow-y-auto */
        }
        .learn-card {
            /* Tailwind: bg-white bg-opacity-90 backdrop-blur-md rounded-2xl p-7 w-full text-center shadow-lg mb-5 */
        }
        .word-image {
            /* Tailwind: w-full max-w-[200px] h-auto rounded-xl mb-5 object-cover mx-auto */
        }
        .word-text {
            /* Tailwind: text-5xl font-bold text-gray-800 mb-2.5 */
        }
        .word-description {
            /* Tailwind: text-base text-gray-600 leading-relaxed mb-5 */
        }
        .action-buttons {
            /* Tailwind: flex justify-between w-full max-w-md px-5 py-5 mx-auto box-border mt-5 */
        }
        .btn-secondary, .btn-primary {
            /* Tailwind: py-3 px-0 border-none rounded-lg text-base font-semibold cursor-pointer transition-all duration-300 ease-in-out flex items-center justify-center gap-2 flex-1 mx-1 */
        }
        .btn-secondary {
            /* Tailwind: bg-gray-200 text-gray-800 hover:bg-gray-300 */
        }
        .btn-primary {
            /* Tailwind: bg-blue-500 text-white hover:bg-blue-600 */
        }
        .learning-tips {
            /* Tailwind: bg-white bg-opacity-20 backdrop-blur-md rounded-xl p-4 mt-5 text-white text-center text-sm leading-relaxed */
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-300 to-cyan-200">
    <div class="status-bar w-full px-5 py-2.5 flex justify-between items-center text-white bg-black bg-opacity-10">
        <span>9:41</span>
        <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-full"></i></span>
    </div>

    <div class="header w-full px-5 py-3.5 flex items-center text-white">
        <a href="#" class="back-btn text-2xl text-white no-underline mr-4"><i class="fas fa-arrow-left"></i></a>
        <h1 class="text-xl font-semibold m-0">学习模式</h1>
    </div>

    <div class="main-content flex-grow w-full max-w-md p-5 mx-auto overflow-y-auto">
        <div class="learn-card bg-white bg-opacity-90 backdrop-blur-md rounded-2xl p-7 w-full text-center shadow-lg mb-5">
            <img src="../images/apple.png" alt="苹果" class="word-image w-full max-w-[200px] h-auto rounded-xl mb-5 object-cover mx-auto">
            <div class="word-text text-5xl font-bold text-gray-800 mb-2.5">苹果</div>
            <div class="word-description text-base text-gray-600 leading-relaxed mb-5">
                一种常见的水果，通常是红色或绿色，味道甜美，富含维生素。
            </div>
        </div>
        <div class="learning-tips bg-white bg-opacity-20 backdrop-blur-md rounded-xl p-4 mt-5 text-white text-center text-sm leading-relaxed">
            <p><i class="fas fa-lightbulb"></i> 学习小技巧：家长可以和孩子一起大声朗读。</p>
        </div>
    </div>

    <div class="action-buttons flex justify-between w-full max-w-md px-5 py-5 mx-auto box-border mt-5">
        <button class="btn-secondary py-3 px-0 border-none rounded-lg text-base font-semibold cursor-pointer transition-all duration-300 ease-in-out flex items-center justify-center gap-2 flex-1 mx-1 bg-gray-200 text-gray-800 hover:bg-gray-300"><i class="fas fa-arrow-left"></i> 上一个</button>
        <button class="btn-primary py-3 px-0 border-none rounded-lg text-base font-semibold cursor-pointer transition-all duration-300 ease-in-out flex items-center justify-center gap-2 flex-1 mx-1 bg-blue-500 text-white hover:bg-blue-600"><i class="fas fa-arrow-right"></i> 下一个</button>
    </div>
    
    <!-- Restored Bottom Nav with Basic Tailwind -->
    <div id="bottom-nav-test" class="fixed bottom-0 left-0 w-full h-20 bg-white shadow-md flex justify-around items-center z-50 border-t border-gray-200">
        <a href="./home.html" class="nav-item flex flex-col items-center justify-center text-gray-600 hover:text-blue-500 p-2">
            <i class="fas fa-home text-2xl mb-1"></i>
            <span class="text-xs font-medium">首页</span>
        </a>
        <a href="./learn.html" class="nav-item flex flex-col items-center justify-center text-blue-500 p-2 border-t-2 border-blue-500">
            <i class="fas fa-book-open text-2xl mb-1"></i>
            <span class="text-xs font-medium">学习</span>
        </a>
        <a href="./progress.html" class="nav-item flex flex-col items-center justify-center text-gray-600 hover:text-blue-500 p-2">
            <i class="fas fa-chart-line text-2xl mb-1"></i>
            <span class="text-xs font-medium">进度</span>
        </a>
        <a href="./settings.html" class="nav-item flex flex-col items-center justify-center text-gray-600 hover:text-blue-500 p-2">
            <i class="fas fa-cog text-2xl mb-1"></i>
            <span class="text-xs font-medium">我的</span>
        </a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LEARN.HTML (Tailwind Restore Test): DOMContentLoaded. document.readyState:', document.readyState);
            
            const bottomNav = document.getElementById('bottom-nav-test'); // Updated ID
            if (bottomNav) {
                console.log('LEARN.HTML (Tailwind Restore Test): Nav element #bottom-nav-test found.');
                const computedStyle = window.getComputedStyle(bottomNav);
                console.log('    Computed display:', computedStyle.getPropertyValue('display'));
                console.log('    Computed visibility:', computedStyle.getPropertyValue('visibility'));
                console.log('    Computed opacity:', computedStyle.getPropertyValue('opacity'));
                console.log('    Computed position:', computedStyle.getPropertyValue('position'));
                console.log('    Computed bottom:', computedStyle.getPropertyValue('bottom'));
                console.log('    Computed left:', computedStyle.getPropertyValue('left'));
                console.log('    Computed width:', computedStyle.getPropertyValue('width'));
                console.log('    Computed height:', computedStyle.getPropertyValue('height'));
                console.log('    Computed z-index:', computedStyle.getPropertyValue('z-index'));
                console.log('    Computed background-color:', computedStyle.getPropertyValue('background-color'));
                const rect = bottomNav.getBoundingClientRect();
                console.log('    BoundingClientRect:', JSON.stringify(rect));
                if (rect.width === 0 || rect.height === 0) {
                    console.warn('LEARN.HTML (Tailwind Restore Test): Nav element has zero width or height!', rect);
                }
                if (computedStyle.getPropertyValue('display') === 'none') {
                     console.warn('LEARN.HTML (Tailwind Restore Test): Nav element computed display is "none"!');
                }

                // Check visibility of spans
                const navSpans = bottomNav.querySelectorAll('.nav-item span');
                navSpans.forEach((span, index) => {
                    const spanStyle = window.getComputedStyle(span);
                    console.log(`    Span ${index + 1} text: "${span.textContent.trim()}", display: ${spanStyle.getPropertyValue('display')}, visibility: ${spanStyle.getPropertyValue('visibility')}, opacity: ${spanStyle.getPropertyValue('opacity')}, color: ${spanStyle.getPropertyValue('color')}`);
                });

            } else {
                console.error('LEARN.HTML (Tailwind Restore Test): Nav element #bottom-nav-test NOT found!');
            }
            console.log('LEARN.HTML (Tailwind Restore Test): DOMContentLoaded script finished.');
        });
    </script>
</body>
</html>
