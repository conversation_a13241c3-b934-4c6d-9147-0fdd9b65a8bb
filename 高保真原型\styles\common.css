/* 儿童识字小程序 - 通用样式 */

/* 全局字体和基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 通用按钮样式 */
.btn {
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background: white;
    color: #667eea;
    padding: 18px 30px;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    padding: 16px 30px;
    font-size: 16px;
    backdrop-filter: blur(10px);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* 状态栏样式 */
.status-bar {
    height: 47px;
    background: rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    color: white;
    font-size: 14px;
    font-weight: 600;
}

/* 底部导航样式 */
.bottom-nav {
    height: 80px;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.2s ease;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item.inactive {
    color: #999;
}

/* 渐变背景 */
.gradient-bg-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-4 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gradient-bg-5 {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.gradient-bg-6 {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.bounce {
    animation: bounce 2s infinite;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .phone-container {
        width: 100%;
        height: 100vh;
        border-radius: 0;
        margin: 0;
        padding: 0;
    }
    
    .phone-screen {
        border-radius: 0;
    }
}

/* 无障碍设计 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-secondary {
        border-width: 3px;
    }
    
    .nav-item.inactive {
        color: #666;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background: #2d3748;
        color: white;
    }
    
    .bottom-nav {
        background: rgba(45, 55, 72, 0.95);
        border-top-color: rgba(255,255,255,0.1);
    }
}
