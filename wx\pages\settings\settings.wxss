/**
 * 设置页面样式
 */

.container {
  min-height: 100vh;
}

.main-content {
  height: 100vh;
  padding: 40rpx;
}

.user-section {
  margin-bottom: 40rpx;
}

.user-card {
  background: rgba(255,255,255,0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
}

.user-info {
  flex: 1;
  color: white;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-age {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

.edit-btn {
  background: rgba(255,255,255,0.3);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.section-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.settings-section, .data-section, .about-section {
  margin-bottom: 40rpx;
}

.settings-group, .data-group, .about-group {
  background: white;
  border-radius: 30rpx;
  padding: 0;
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.setting-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.setting-btn {
  background: #f0f0f0;
  color: #667eea;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.data-item, .about-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child, .about-item:last-child {
  border-bottom: none;
}

.data-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f0f0f0;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 30rpx;
}

.data-info {
  flex: 1;
}

.data-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.data-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.data-arrow, .about-arrow {
  font-size: 32rpx;
  color: #ccc;
}

.about-label {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.about-value {
  font-size: 28rpx;
  color: #666;
}

.icon-text {
  font-size: inherit;
}
