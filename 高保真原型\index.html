<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>儿童识字小程序 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 尺寸模拟 */
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 47px;
            background: #000;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 39px;
            overflow: hidden;
            background: #fff;
            position: relative;
        }
        
        .status-bar {
            height: 47px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .prototype-item {
            text-align: center;
        }
        
        .prototype-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .main-title {
            text-align: center;
            padding: 40px 20px 20px;
            color: #333;
        }
        
        .main-title h1 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .main-title p {
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-title">
        <h1>儿童识字小程序</h1>
        <p>高保真原型展示 - 所有界面预览</p>
    </div>
    
    <div class="prototype-grid">
        <!-- 首页 -->
        <div class="prototype-item">
            <div class="prototype-title">首页 - 学习入口</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/home.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 字词输入页 -->
        <div class="prototype-item">
            <div class="prototype-title">字词输入 - 每日学习准备</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/input.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 学习模式 -->
        <div class="prototype-item">
            <div class="prototype-title">学习模式 - 字词展示</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/learn.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 复习模式 -->
        <div class="prototype-item">
            <div class="prototype-title">复习模式 - 认识判断</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/review.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 回顾模式 -->
        <div class="prototype-item">
            <div class="prototype-title">回顾模式 - 重点复习</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/recall.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 学习记录 -->
        <div class="prototype-item">
            <div class="prototype-title">学习记录 - 进度统计</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/progress.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 设置页面 -->
        <div class="prototype-item">
            <div class="prototype-title">设置页面 - 个人中心</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/settings.html"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 完成页面 -->
        <div class="prototype-item">
            <div class="prototype-title">完成页面 - 学习结束</div>
            <div class="phone-container">
                <div class="phone-screen">
                    <iframe src="pages/complete.html"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
